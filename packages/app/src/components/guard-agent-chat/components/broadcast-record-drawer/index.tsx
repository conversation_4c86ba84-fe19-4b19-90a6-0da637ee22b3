import React, { useEffect, useState, useRef } from 'react';
import { ChatDrawer } from '@tencent/cloud-chat-ui';
import { Table, Text, TagSearchBox, message, StatusTip } from '@tencent/tea-component';
import { debounce } from 'lodash';
import { t } from '@tea/app/i18n';
import {
  describeBroadcastMessageHistory,
} from '@src/service/api/broadcast-agent-chat';
import './index.less';
const { expandable, pageable, sortable } = Table.addons;

interface Props {
  visible: boolean,
  archId: string,
  broadcastId: string,
  onClose: () => void
}

enum columnsEnum {
  Title = 'Title',
  BroadcastMessageId = 'BroadcastMessageId',
  CreateTime = 'CreateTime',
  Content = 'Content'
}

enum pageEnum {
  pageSize = 10,
  pageNumber = 1,
}


const BroadcastRecordDrawer = ({ visible, archId, broadcastId, onClose }: Props) => {
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [records, setRecords] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize, setPageSize] = useState(pageEnum.pageSize);
  const [pageNumber, setPageNumber] = useState(pageEnum.pageNumber);
  const [filters, setFilters] = useState([]);
  const [onOff, setOnOff] = useState(false);
  const [loading, setLoading] = useState(false);
  const initBroadcastIdRef = useRef('');
  const [sorts, setSorts] = useState([]);
  const reset = ()=>{
    initBroadcastIdRef.current = '';
    setSorts([]);
    setExpandedKeys([]);
    setFilters([]);
    setOnOff(false);
    setLoading(false);
    setPageSize(pageEnum.pageSize);
    setPageNumber(pageEnum.pageNumber);
  };
  const attributes: any = [
    {
      type: 'input',
      key: columnsEnum.Title,
      name: t('模板名称'),
    },
    {
      type: 'input',
      key: columnsEnum.BroadcastMessageId,
      name: t('播报ID'),
    },
  ];
  const columns = [
    {
      key: columnsEnum.BroadcastMessageId,
      header: t('播报ID'),
    },
    {
      key: columnsEnum.Title,
      header: t('播报模板'),
      render: item => <Text theme={'primary'}>{item[columnsEnum.Title] || '-'}</Text>,
    },
    {
      key: columnsEnum.CreateTime,
      header: t('播报时间'),
      width: 160,
      render: item => <div>{item[columnsEnum.CreateTime] || '-'}</div>,
    },
  ];

  const getDescribeBroadcastMessageHistory = async () => {
    let title = '';
    let broadcastMessageId = '';
    filters?.forEach((item) => {
      if (item.attr.key === 'Title') {
        title = item.values[0]?.name ?? '';
      }
      if (item.attr.key === 'BroadcastMessageId') {
        broadcastMessageId = item.values[0]?.name ?? '';
      }
    });
    setLoading(true);
    const res: any = await describeBroadcastMessageHistory({
      ArchId: archId,
      Title: title,
      BroadcastMessageId: broadcastMessageId,
      Limit: pageSize,
      Offset: (pageNumber - 1) * pageSize,
      OrderByTime: sorts?.[0]?.order === 'asc'
    });
    if (res.Error) {
      setLoading(false);
      const msg = res.Error.Message;
      message.error({ content: msg });
      return;
    };
    if (initBroadcastIdRef.current && res.BroadcastMessageList?.length > 0) {
      setExpandedKeys(res.BroadcastMessageList?.map((item, i)=>{
        return `record_${i}`;
      }) || []);
      initBroadcastIdRef.current = '';
    } else {
      setExpandedKeys([]);
    }
    setRecords(res.BroadcastMessageList);
    setTotalCount(res.TotalCount);
    setLoading(false);
  };
  useEffect(() => {
    const debouncedFetch = debounce(() => {
      if (visible) {
        getDescribeBroadcastMessageHistory();
      }
    }, 100);
    debouncedFetch();
    return () => {
      debouncedFetch?.cancel();
    };
  }, [filters, pageSize, pageNumber, visible, sorts, onOff]);
  useEffect(()=>{
    if (!visible) {
      reset();
    }
  }, [visible]);
  useEffect(() => {
    if (broadcastId) {
      initBroadcastIdRef.current = broadcastId;
      setFilters([
        {
          attr: {
            ...attributes[1],
          },
          values: [
            {
              name: broadcastId,
            },
          ],
        },
      ]);
    }
  }, [broadcastId]);

  return <ChatDrawer
    className={'broadcast-record-drawer-wrap'}
    visible={visible}
    onClose={() => onClose?.()}
    title={t('播报记录')}
  >
    <TagSearchBox
      className={'broadcast-record-search-box'}
      attributes={attributes}
      minWidth="100%"
      value={filters}
      onSearchButtonClick={
        () => {
          setOnOff(!onOff);
        }
      }
      onChange={
        (tags) => {
          const len = tags?.length;
          if (len === 1 && tags[0]?.attr === null) {
            setFilters([
              {
                attr: {
                  ...attributes[0],
                },
                values: tags[0].values,
              },
            ]);
          } else {
            const obj: any = {};
            let keyTem = '';
            tags?.forEach((item, i) => {
              if (item.attr !== null) {
                keyTem = item.attr.key;
                obj[keyTem] = {
                  attr: item.attr,
                };
                obj[keyTem].values = item.values;
              }
              if (item?.attr === null && (i === (len - 1) || (tags[i + 1] && tags[i + 1]?.attr !== null))) {
                obj[keyTem].values = item.values;
              }
            });
            setPageNumber(1);
            setFilters(Object.keys(obj)?.map(item => obj[item]) || []);
          }
        }
      }
    />
    <Table
      records={records}
      columns={columns}
      topTip={
        (loading || records?.length == 0) && <StatusTip status={loading ? 'loading' : 'empty'} />
      }
      addons={[
        expandable({
          rowExpand: true,
          expandedKeys,
          onExpandedKeysChange: (keys, { event }) => {
            event.stopPropagation();
            setExpandedKeys(keys);
          },
          render(record) {
            return <div className={'message-con-wrap'}>
              {
                record[columnsEnum.Content] || '-'
              }
            </div>;
          },
          gapCell: 0,
          gapCellRender: () => 'gapCellRender',
        }),
        pageable({
          recordCount: totalCount,
          pageIndex: pageNumber,
          pageSize,
          onPagingChange: ({ pageSize, pageIndex }) => {
            setPageSize(pageSize);
            setPageNumber(pageIndex);
          },
        }),
        sortable({
          // 这两列支持排序，其中 age 列优先倒序
          columns: ["CreateTime"],
          value: sorts,
          onChange: value => {
            setPageNumber(1);
            setSorts(value);
          },
        }),
      ]}
      />
  </ChatDrawer>;
};

export default BroadcastRecordDrawer;
