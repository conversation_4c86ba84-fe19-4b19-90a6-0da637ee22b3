.charts-edit-card {
  min-width: 350px;
  box-sizing: border-box;
  padding-right: 20px;

  .charts-card-action {
    .charts-card-action-textarea {
      border: 1px solid;
      border-image-source: linear-gradient(271.1deg, #3A89FF 30.8%, #A283FF 98.12%);
      border-image-slice: 1;
      font-size: 13px;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 4px; // 滚动条宽度
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1; // 滚动条轨道背景色
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1; // 滚动条滑块颜色
        border-radius: 2px;

        &:hover {
          background: #a8a8a8; // 滚动条滑块悬停颜色
        }
      }
    }

    .charts-card-perview-tip {
      color: rgba(0, 0, 0, 0.9);
      border-bottom: 1px dashed #e8e8e8;
      line-height: 21px;
      margin: 5px 0 16px 0;
      padding-bottom: 5px;
    }
    .charts-card-form {
      display: flex;
    }

    .charts-card-action-btn {
      display: flex;
      padding-top: 6px;

      .charts-card-action-btn-item {
        width: 48%;
        height: 36px;
        font-size: 12px;
        margin-right: 12px
      }

      .charts-card-action-btn-fix-item {
        width: 48%;
        height: 36px;
        font-size: 12px;
      }
    }
  }
}


