import {
  message,
} from '@tencent/tea-component';
import React from 'react';
import { Button, Textarea } from 'tdesign-react';
import { useDispatch } from 'react-redux';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import './index.less';
import { t } from '@tea/app/i18n';

interface IProps {
  perviewDetail: any;
  id: string;
  stepKey: string;
  onSendMessage: (message: string, intention?: string) => void;
}
const TemplateEditPanel = ({ onSendMessage }: IProps) => {
  const {
    editTemplateContent,
  }  = useCommonSelector();

  const dispatch = useDispatch();

  const handleBroadcastTemplateSend = () => {
    // 保存模板逻辑
    if (!editTemplateContent) {
      message.warning({ content: t('请输入内容') });
      return;
    }
    onSendMessage?.(editTemplateContent, 'TemplateContentCheckNode');
    dispatch(changeCommonData({
      isTemplateEdit: false,
      editMessageId: '',
      editTemplateContent: '',
    }));
  };

  return <div className='charts-edit-card'>
    <div className='charts-card-action'>
      <Textarea
        className='charts-card-action-textarea'
        autofocus
        autosize={{ minRows: 10 }}
        placeholder={t('请输入内容')}
        value={editTemplateContent}
        onChange={(val) => {
          dispatch(changeCommonData({ editTemplateContent: val }));
        }}
      />
      <p className='charts-card-perview-tip'>{t('*可直接输入指标，也可点击“订阅配置”获取')}</p>
      <div className='charts-card-action-btn'>
        <Button
          shape="rectangle"
          size="medium"
          type="button"
          variant="base"
          className='charts-card-action-btn-item'
          onClick={handleBroadcastTemplateSend}
        >
          {t('保存')}
        </Button>
        <Button
          theme="default"
          variant="outline"
          className='charts-card-action-btn-fix-item'
          onClick={() => {
            dispatch(changeCommonData({
              isTemplateEdit: false,
              editMessageId: '',
              editTemplateContent: '',
            }));
          }}
        >
          {t('取消')}
        </Button>
      </div>
    </div>
  </div>;
};

export default TemplateEditPanel;
