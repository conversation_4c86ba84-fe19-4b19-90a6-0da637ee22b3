/**
 * @fileoverview CLOUD-INSPECTION SDK 入口文件
 */
import './i18n';
import render from './render';
import '@tencent/tea-component/dist/tea.css';
import 'tdesign-react/es/style/index.css';
// 导入依赖
import { app } from '@tencent/tea-app';

// 注册 SDK 入口，提供 SDK 工厂方法
app.sdk.register('capacity-monitoring-sdk', () =>
  // 返回 SDK 对外暴露的 API
  ({
    /**
     * SDK 测通方法，可保留可不保留
     */
    hello() {
      return 'Hello from CLOUD-INSPECTION SDK';
    },
    ...render,
  }));
