import { map, reduce, toPairs /* includes, omit */ } from 'lodash-es';
import { message } from '@tencent/tea-component';
import moment from 'moment';
import { t } from '@tea/app/i18n';

/**
 * 从当前 URL 的搜索参数中获取特定参数的值。
 *
 * @param {string} name 要查找的参数名称。
 * @param {Location} location 用于获取搜索参数的位置对象。
 * @return {?string} 参数的值，如果未找到则为 null。
 */
export function getSearchParam(name, location) {
  const { search } = location;
  const nameArr = search.split(`${name}=`);
  if (nameArr.length === 2) {
    const searchParamArr = nameArr[1].split('&');
    return searchParamArr[0];
  }
  return null;
}

export function getUrlParamFromLocation(nameList, location) {
  const retObj = {};
  map(nameList, (item) => {
    retObj[item] = getSearchParam(item, location);
  });
  return retObj;
}

export function getUrlParamsStr(paramObj) {
  return reduce(toPairs(paramObj), (ret, [key, value]) => {
    ret.push(`${key}=${value}`);
    return ret;
  }, []).join('&');
}

export const queryStringObj = (str) => {
  if (!str) return {};
  const arrUrl = str?.split('?')[1].split('&');
  const objUrl = {};
  for (let m = 0; m < arrUrl.length; m++) {
    // eslint-disable-next-line prefer-destructuring
    objUrl[arrUrl[m].split('=')[0]] = arrUrl[m].split('=')[1];
  }
  return objUrl;
};

export const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

export const isDev = process.env.NODE_ENV === 'development';

// 文字复制到剪切板
export const copyTextToClipboard = (text: string) => {
  // 使用 Clipboard API
  navigator.clipboard
    .writeText(text)
    .then(() => {
      message.success({ content: t('复制成功') });
    })
    .catch(() => {
      message.error({ content: t('复制失败') });
    });
};

export const rejectError = (msg: string) => {
  message.error({ content: msg });
  return Promise.reject(msg);
};

export const getCurrTimeToStr = () => moment(Date.now()).format('YY-MM-DD HH:mm:ss');
